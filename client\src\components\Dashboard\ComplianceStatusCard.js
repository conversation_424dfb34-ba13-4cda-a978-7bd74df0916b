import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Grid,
} from '@mui/material';
import {
  Gavel as ComplianceIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';

const ComplianceStatusCard = () => {
  const complianceFrameworks = [
    {
      name: 'NIST CSF',
      fullName: 'NIST Cybersecurity Framework',
      score: 92,
      status: 'compliant',
      lastAudit: '2024-01-15',
      nextAudit: '2024-07-15',
    },
    {
      name: 'ISO 27001',
      fullName: 'ISO/IEC 27001:2013',
      score: 87,
      status: 'compliant',
      lastAudit: '2024-02-01',
      nextAudit: '2024-08-01',
    },
    {
      name: 'COBIT 2019',
      fullName: 'COBIT 2019 Framework',
      score: 78,
      status: 'partial',
      lastAudit: '2024-01-20',
      nextAudit: '2024-07-20',
    },
    {
      name: 'RBI Guidelines',
      fullName: 'RBI Cyber Security Framework',
      score: 85,
      status: 'compliant',
      lastAudit: '2024-02-10',
      nextAudit: '2024-08-10',
    },
  ];

  const overallCompliance = Math.round(
    complianceFrameworks.reduce((sum, framework) => sum + framework.score, 0) / 
    complianceFrameworks.length
  );

  const getStatusColor = (status) => {
    switch (status) {
      case 'compliant': return 'success';
      case 'partial': return 'warning';
      case 'non-compliant': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'compliant': return <CheckIcon />;
      case 'partial': return <WarningIcon />;
      case 'non-compliant': return <ErrorIcon />;
      default: return <ScheduleIcon />;
    }
  };

  const getScoreColor = (score) => {
    if (score >= 90) return 'success';
    if (score >= 75) return 'warning';
    return 'error';
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <ComplianceIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h6">Compliance Status</Typography>
        </Box>

        {/* Overall Compliance Score */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="subtitle2">Overall Compliance</Typography>
            <Typography variant="subtitle2" color={`${getScoreColor(overallCompliance)}.main`}>
              {overallCompliance}%
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={overallCompliance}
            color={getScoreColor(overallCompliance)}
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>

        {/* Framework Details */}
        <Typography variant="subtitle2" gutterBottom>
          Framework Compliance
        </Typography>
        
        <List dense>
          {complianceFrameworks.map((framework, index) => (
            <ListItem key={index} sx={{ px: 0, py: 1 }}>
              <ListItemIcon sx={{ minWidth: 36 }}>
                {getStatusIcon(framework.status)}
              </ListItemIcon>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Typography variant="body2" fontWeight="medium">
                      {framework.name}
                    </Typography>
                    <Chip
                      label={`${framework.score}%`}
                      color={getScoreColor(framework.score)}
                      size="small"
                      variant="outlined"
                    />
                  </Box>
                }
                secondary={
                  <Box>
                    <Typography variant="caption" color="text.secondary" display="block">
                      {framework.fullName}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Next audit: {new Date(framework.nextAudit).toLocaleDateString()}
                    </Typography>
                  </Box>
                }
              />
            </ListItem>
          ))}
        </List>

        {/* Compliance Summary */}
        <Box sx={{ mt: 2, p: 2, bgcolor: 'action.hover', borderRadius: 1 }}>
          <Grid container spacing={2}>
            <Grid item xs={4}>
              <Typography variant="caption" color="text.secondary" display="block">
                Compliant
              </Typography>
              <Typography variant="h6" color="success.main">
                {complianceFrameworks.filter(f => f.status === 'compliant').length}
              </Typography>
            </Grid>
            <Grid item xs={4}>
              <Typography variant="caption" color="text.secondary" display="block">
                Partial
              </Typography>
              <Typography variant="h6" color="warning.main">
                {complianceFrameworks.filter(f => f.status === 'partial').length}
              </Typography>
            </Grid>
            <Grid item xs={4}>
              <Typography variant="caption" color="text.secondary" display="block">
                Non-Compliant
              </Typography>
              <Typography variant="h6" color="error.main">
                {complianceFrameworks.filter(f => f.status === 'non-compliant').length}
              </Typography>
            </Grid>
          </Grid>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ComplianceStatusCard;
