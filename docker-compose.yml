version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cyberguard-postgres
    environment:
      POSTGRES_DB: cyberguard
      POSTGRES_USER: cyberguard_user
      POSTGRES_PASSWORD: secure_password_123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - cyberguard-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: cyberguard-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cyberguard-network

  # Elasticsearch for SIEM
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: cyberguard-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - cyberguard-network

  # Kibana for SIEM Dashboard
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: cyberguard-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - cyberguard-network

  # Logstash for Log Processing
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: cyberguard-logstash
    volumes:
      - ./elk/logstash/pipeline:/usr/share/logstash/pipeline
      - ./elk/logstash/config:/usr/share/logstash/config
    ports:
      - "5044:5044"
      - "9600:9600"
    depends_on:
      - elasticsearch
    networks:
      - cyberguard-network

  # Main Application Server
  app:
    build: .
    container_name: cyberguard-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=cyberguard
      - DB_USER=cyberguard_user
      - DB_PASSWORD=secure_password_123
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - ELASTICSEARCH_HOST=elasticsearch
      - ELASTICSEARCH_PORT=9200
    depends_on:
      - postgres
      - redis
      - elasticsearch
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - cyberguard-network

  # OWASP ZAP for Security Testing
  zap:
    image: owasp/zap2docker-stable
    container_name: cyberguard-zap
    ports:
      - "8080:8080"
    command: zap-webswing.sh
    networks:
      - cyberguard-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: cyberguard-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - cyberguard-network

  # Vulnerability Scanner (OpenVAS)
  openvas:
    image: mikesplain/openvas
    container_name: cyberguard-openvas
    ports:
      - "9390:9390"
      - "9391:9391"
    environment:
      - OV_PASSWORD=admin123
    volumes:
      - openvas_data:/var/lib/openvas
    networks:
      - cyberguard-network

volumes:
  postgres_data:
  redis_data:
  elasticsearch_data:
  openvas_data:

networks:
  cyberguard-network:
    driver: bridge
