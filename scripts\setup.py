#!/usr/bin/env python3
"""
CyberGuard Pro - Automated Setup Script
Comprehensive setup and configuration for the cybersecurity platform
"""

import os
import sys
import subprocess
import json
import logging
import argparse
import platform
from pathlib import Path
import shutil
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CyberGuardSetup:
    """Automated setup and configuration for CyberGuard Pro"""
    
    def __init__(self, environment='development'):
        self.environment = environment
        self.project_root = Path(__file__).parent.parent
        self.os_type = platform.system().lower()
        self.setup_complete = False
        
    def check_prerequisites(self):
        """Check if all prerequisites are installed"""
        logger.info("Checking prerequisites...")
        
        prerequisites = {
            'node': {'command': 'node --version', 'min_version': '18.0.0'},
            'npm': {'command': 'npm --version', 'min_version': '9.0.0'},
            'python': {'command': 'python --version', 'min_version': '3.9.0'},
            'docker': {'command': 'docker --version', 'min_version': '20.10.0'},
            'docker-compose': {'command': 'docker-compose --version', 'min_version': '2.0.0'}
        }
        
        missing_deps = []
        
        for tool, config in prerequisites.items():
            try:
                result = subprocess.run(
                    config['command'].split(),
                    capture_output=True,
                    text=True,
                    check=True
                )
                logger.info(f"✅ {tool}: {result.stdout.strip()}")
            except (subprocess.CalledProcessError, FileNotFoundError):
                logger.error(f"❌ {tool}: Not found or version too old")
                missing_deps.append(tool)
        
        if missing_deps:
            logger.error(f"Missing prerequisites: {', '.join(missing_deps)}")
            self._print_installation_instructions(missing_deps)
            return False
        
        logger.info("✅ All prerequisites satisfied")
        return True
    
    def _print_installation_instructions(self, missing_deps):
        """Print installation instructions for missing dependencies"""
        instructions = {
            'node': 'Install Node.js from https://nodejs.org/',
            'npm': 'npm is included with Node.js',
            'python': 'Install Python from https://python.org/',
            'docker': 'Install Docker from https://docker.com/',
            'docker-compose': 'Install Docker Compose from https://docs.docker.com/compose/'
        }
        
        print("\n📋 Installation Instructions:")
        for dep in missing_deps:
            print(f"  • {dep}: {instructions.get(dep, 'Please install manually')}")
    
    def setup_environment(self):
        """Setup environment configuration"""
        logger.info("Setting up environment configuration...")
        
        env_file = self.project_root / '.env'
        env_example = self.project_root / '.env.example'
        
        if not env_file.exists() and env_example.exists():
            shutil.copy(env_example, env_file)
            logger.info("✅ Created .env file from template")
        
        # Generate secure JWT secret
        import secrets
        jwt_secret = secrets.token_urlsafe(64)
        
        # Update .env file with generated values
        if env_file.exists():
            with open(env_file, 'r') as f:
                content = f.read()
            
            # Replace placeholder values
            content = content.replace(
                'your_super_secret_jwt_key_here_change_in_production',
                jwt_secret
            )
            content = content.replace(
                'secure_password_123',
                secrets.token_urlsafe(16)
            )
            
            with open(env_file, 'w') as f:
                f.write(content)
            
            logger.info("✅ Environment configuration updated")
    
    def install_dependencies(self):
        """Install all project dependencies"""
        logger.info("Installing project dependencies...")
        
        # Install backend dependencies
        logger.info("Installing backend dependencies...")
        subprocess.run(['npm', 'install'], cwd=self.project_root, check=True)
        
        # Install frontend dependencies
        client_dir = self.project_root / 'client'
        if client_dir.exists():
            logger.info("Installing frontend dependencies...")
            subprocess.run(['npm', 'install'], cwd=client_dir, check=True)
        
        # Install Python dependencies
        scripts_dir = self.project_root / 'scripts'
        requirements_file = scripts_dir / 'requirements.txt'
        if requirements_file.exists():
            logger.info("Installing Python dependencies...")
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)
            ], check=True)
        
        logger.info("✅ All dependencies installed")
    
    def setup_database(self):
        """Setup database and initial data"""
        logger.info("Setting up database...")
        
        if self.environment == 'development':
            # Use Docker for development
            logger.info("Starting database services with Docker...")
            subprocess.run([
                'docker-compose', 'up', '-d', 'postgres', 'redis'
            ], cwd=self.project_root, check=True)
            
            # Wait for database to be ready
            logger.info("Waiting for database to be ready...")
            time.sleep(10)
        
        # Run database migrations (if available)
        migration_script = self.project_root / 'database' / 'migrate.js'
        if migration_script.exists():
            logger.info("Running database migrations...")
            subprocess.run(['node', str(migration_script)], cwd=self.project_root)
        
        logger.info("✅ Database setup completed")
    
    def setup_security_tools(self):
        """Setup and configure security tools"""
        logger.info("Setting up security tools...")
        
        # Make Python scripts executable
        script_dirs = ['vapt-tools', 'grc-framework', 'network-security', 'app-security']
        
        for script_dir in script_dirs:
            dir_path = self.project_root / script_dir
            if dir_path.exists():
                for script_file in dir_path.glob('*.py'):
                    script_file.chmod(0o755)
                    logger.info(f"Made executable: {script_file.name}")
        
        # Create necessary directories
        directories = ['logs', 'uploads', 'backups', 'reports']
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(exist_ok=True)
            logger.info(f"Created directory: {directory}")
        
        logger.info("✅ Security tools configured")
    
    def setup_monitoring(self):
        """Setup monitoring and logging"""
        logger.info("Setting up monitoring and logging...")
        
        if self.environment == 'development':
            # Start ELK stack
            logger.info("Starting ELK stack...")
            subprocess.run([
                'docker-compose', 'up', '-d', 'elasticsearch', 'kibana', 'logstash'
            ], cwd=self.project_root, check=True)
        
        # Configure log rotation
        if self.os_type == 'linux':
            self._setup_log_rotation()
        
        logger.info("✅ Monitoring setup completed")
    
    def _setup_log_rotation(self):
        """Setup log rotation for Linux systems"""
        logrotate_config = """
/path/to/cyberguard/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 cyberguard cyberguard
}
"""
        logger.info("Log rotation configuration available for manual setup")
    
    def run_initial_tests(self):
        """Run initial tests to verify setup"""
        logger.info("Running initial tests...")
        
        # Test backend
        try:
            subprocess.run(['npm', 'test'], cwd=self.project_root, check=True)
            logger.info("✅ Backend tests passed")
        except subprocess.CalledProcessError:
            logger.warning("⚠️ Some backend tests failed")
        
        # Test security tools
        vapt_dir = self.project_root / 'vapt-tools'
        if vapt_dir.exists():
            test_script = vapt_dir / 'network_scanner.py'
            if test_script.exists():
                try:
                    subprocess.run([
                        sys.executable, str(test_script), '--help'
                    ], check=True, capture_output=True)
                    logger.info("✅ VAPT tools working")
                except subprocess.CalledProcessError:
                    logger.warning("⚠️ VAPT tools may have issues")
        
        logger.info("✅ Initial tests completed")
    
    def generate_setup_report(self):
        """Generate setup completion report"""
        report = {
            'setup_date': time.strftime('%Y-%m-%d %H:%M:%S'),
            'environment': self.environment,
            'platform': platform.platform(),
            'python_version': sys.version,
            'project_root': str(self.project_root),
            'status': 'completed' if self.setup_complete else 'failed',
            'next_steps': [
                'Start the application: npm run dev',
                'Access dashboard: http://localhost:3001',
                'Review documentation in docs/ directory',
                'Configure external integrations as needed'
            ]
        }
        
        report_file = self.project_root / 'setup_report.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"✅ Setup report saved: {report_file}")
        return report
    
    def run_complete_setup(self):
        """Run complete setup process"""
        logger.info("🚀 Starting CyberGuard Pro setup...")
        
        try:
            # Check prerequisites
            if not self.check_prerequisites():
                return False
            
            # Setup steps
            self.setup_environment()
            self.install_dependencies()
            self.setup_database()
            self.setup_security_tools()
            self.setup_monitoring()
            self.run_initial_tests()
            
            self.setup_complete = True
            report = self.generate_setup_report()
            
            # Print success message
            self._print_success_message(report)
            return True
            
        except Exception as e:
            logger.error(f"❌ Setup failed: {str(e)}")
            return False
    
    def _print_success_message(self, report):
        """Print setup success message"""
        print(f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎉 Setup Completed Successfully! 🎉       ║
    ╠══════════════════════════════════════════════════════════════╣
    ║                                                              ║
    ║  CyberGuard Pro is now ready for use!                       ║
    ║                                                              ║
    ║  Next Steps:                                                 ║
    ║  1. Start the application: npm run dev                       ║
    ║  2. Access dashboard: http://localhost:3001                  ║
    ║  3. Login with default credentials (see README.md)           ║
    ║  4. Explore the security features and tools                  ║
    ║                                                              ║
    ║  Documentation:                                              ║
    ║  • User Guide: documentation/USER_MANUAL.md                 ║
    ║  • Deployment: documentation/DEPLOYMENT_GUIDE.md            ║
    ║  • Security Policies: documentation/SECURITY_POLICIES.md    ║
    ║                                                              ║
    ║  Support: Check logs/ directory for troubleshooting         ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
        """)

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description='CyberGuard Pro Setup Script'
    )
    parser.add_argument(
        '--environment',
        choices=['development', 'production'],
        default='development',
        help='Setup environment'
    )
    parser.add_argument(
        '--skip-tests',
        action='store_true',
        help='Skip initial tests'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Verbose output'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Run setup
    setup = CyberGuardSetup(args.environment)
    success = setup.run_complete_setup()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
