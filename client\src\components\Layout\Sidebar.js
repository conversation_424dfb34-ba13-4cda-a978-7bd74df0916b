import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Box,
  Collapse,
  Typography,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Security as SecurityIcon,
  BugReport as IncidentIcon,
  Assessment as VulnIcon,
  Gavel as ComplianceIcon,
  RiskAssessment as RiskIcon,
  NetworkCheck as NetworkIcon,
  Code as AppSecIcon,
  Settings as SettingsIcon,
  ExpandLess,
  ExpandMore,
  Event as EventIcon,
  Shield as ShieldIcon,
  Policy as PolicyIcon,
} from '@mui/icons-material';

const drawerWidth = 240;
const collapsedWidth = 60;

const Sidebar = ({ open, onToggle }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [expandedItems, setExpandedItems] = React.useState({});

  const handleItemClick = (path) => {
    navigate(path);
  };

  const handleExpandClick = (item) => {
    setExpandedItems(prev => ({
      ...prev,
      [item]: !prev[item]
    }));
  };

  const menuItems = [
    {
      text: 'Dashboard',
      icon: <DashboardIcon />,
      path: '/dashboard',
    },
    {
      text: 'Security Operations',
      icon: <SecurityIcon />,
      expandable: true,
      children: [
        {
          text: 'Security Events',
          icon: <EventIcon />,
          path: '/security-events',
        },
        {
          text: 'Incident Management',
          icon: <IncidentIcon />,
          path: '/incidents',
        },
      ],
    },
    {
      text: 'Vulnerability Management',
      icon: <VulnIcon />,
      path: '/vulnerability-assessment',
    },
    {
      text: 'GRC & Compliance',
      icon: <ComplianceIcon />,
      expandable: true,
      children: [
        {
          text: 'Compliance Reports',
          icon: <PolicyIcon />,
          path: '/compliance',
        },
        {
          text: 'Risk Assessment',
          icon: <RiskIcon />,
          path: '/risk-assessment',
        },
      ],
    },
    {
      text: 'Security Controls',
      icon: <ShieldIcon />,
      expandable: true,
      children: [
        {
          text: 'Network Security',
          icon: <NetworkIcon />,
          path: '/network-security',
        },
        {
          text: 'Application Security',
          icon: <AppSecIcon />,
          path: '/app-security',
        },
      ],
    },
  ];

  const bottomMenuItems = [
    {
      text: 'Settings',
      icon: <SettingsIcon />,
      path: '/settings',
    },
  ];

  const renderMenuItem = (item, index) => {
    const isSelected = location.pathname === item.path;
    const isExpanded = expandedItems[item.text];

    if (item.expandable) {
      return (
        <React.Fragment key={item.text}>
          <ListItem disablePadding>
            <ListItemButton
              onClick={() => handleExpandClick(item.text)}
              sx={{
                minHeight: 48,
                justifyContent: open ? 'initial' : 'center',
                px: 2.5,
              }}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: open ? 3 : 'auto',
                  justifyContent: 'center',
                }}
              >
                {item.icon}
              </ListItemIcon>
              {open && (
                <>
                  <ListItemText primary={item.text} />
                  {isExpanded ? <ExpandLess /> : <ExpandMore />}
                </>
              )}
            </ListItemButton>
          </ListItem>
          {open && (
            <Collapse in={isExpanded} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                {item.children.map((child) => (
                  <ListItem key={child.text} disablePadding>
                    <ListItemButton
                      onClick={() => handleItemClick(child.path)}
                      sx={{
                        pl: 4,
                        backgroundColor: location.pathname === child.path ? 'action.selected' : 'transparent',
                        '&:hover': {
                          backgroundColor: 'action.hover',
                        },
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 40 }}>
                        {child.icon}
                      </ListItemIcon>
                      <ListItemText primary={child.text} />
                    </ListItemButton>
                  </ListItem>
                ))}
              </List>
            </Collapse>
          )}
        </React.Fragment>
      );
    }

    return (
      <ListItem key={item.text} disablePadding>
        <ListItemButton
          onClick={() => handleItemClick(item.path)}
          sx={{
            minHeight: 48,
            justifyContent: open ? 'initial' : 'center',
            px: 2.5,
            backgroundColor: isSelected ? 'action.selected' : 'transparent',
            '&:hover': {
              backgroundColor: 'action.hover',
            },
          }}
        >
          <ListItemIcon
            sx={{
              minWidth: 0,
              mr: open ? 3 : 'auto',
              justifyContent: 'center',
              color: isSelected ? 'primary.main' : 'inherit',
            }}
          >
            {item.icon}
          </ListItemIcon>
          {open && (
            <ListItemText 
              primary={item.text}
              sx={{
                color: isSelected ? 'primary.main' : 'inherit',
              }}
            />
          )}
        </ListItemButton>
      </ListItem>
    );
  };

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: open ? drawerWidth : collapsedWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: open ? drawerWidth : collapsedWidth,
          boxSizing: 'border-box',
          transition: 'width 0.3s ease',
          overflowX: 'hidden',
        },
      }}
    >
      <Box sx={{ mt: 8 }}>
        {open && (
          <Box sx={{ p: 2 }}>
            <Typography variant="h6" color="primary" gutterBottom>
              Security Operations
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Comprehensive Cybersecurity Management
            </Typography>
          </Box>
        )}
        
        <List>
          {menuItems.map((item, index) => renderMenuItem(item, index))}
        </List>
        
        <Divider />
        
        <List>
          {bottomMenuItems.map((item, index) => renderMenuItem(item, index))}
        </List>
      </Box>
    </Drawer>
  );
};

export default Sidebar;
