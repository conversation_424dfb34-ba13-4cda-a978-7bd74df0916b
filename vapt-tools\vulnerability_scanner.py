#!/usr/bin/env python3
"""
CyberGuard Pro - Vulnerability Scanner
Comprehensive vulnerability assessment tool
Integrates with OpenVAS, Nessus, and custom checks
"""

import requests
import json
import xml.etree.ElementTree as ET
import subprocess
import os
import sys
import logging
import argparse
from datetime import datetime
from typing import Dict, List, Optional
import concurrent.futures
import socket
import ssl
import urllib3
from urllib.parse import urlparse

# Disable SSL warnings for testing
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('../logs/vulnerability_scanner.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class VulnerabilityScanner:
    """
    Advanced vulnerability scanner implementing multiple assessment techniques
    Demonstrates comprehensive VAPT capabilities for ReBIT position
    """
    
    def __init__(self, target: str, scan_type: str = 'comprehensive'):
        self.target = target
        self.scan_type = scan_type
        self.results = {
            'scan_info': {
                'target': target,
                'scan_type': scan_type,
                'timestamp': datetime.now().isoformat(),
                'scanner': 'CyberGuard Pro Vulnerability Scanner'
            },
            'vulnerabilities': [],
            'summary': {},
            'recommendations': []
        }
        
        # OWASP Top 10 vulnerability categories
        self.owasp_categories = {
            'A01': 'Broken Access Control',
            'A02': 'Cryptographic Failures',
            'A03': 'Injection',
            'A04': 'Insecure Design',
            'A05': 'Security Misconfiguration',
            'A06': 'Vulnerable and Outdated Components',
            'A07': 'Identification and Authentication Failures',
            'A08': 'Software and Data Integrity Failures',
            'A09': 'Security Logging and Monitoring Failures',
            'A10': 'Server-Side Request Forgery (SSRF)'
        }
    
    def check_ssl_vulnerabilities(self, host: str, port: int = 443) -> List[Dict]:
        """
        Check for SSL/TLS vulnerabilities
        - Weak cipher suites
        - Certificate issues
        - Protocol vulnerabilities
        """
        vulnerabilities = []
        
        try:
            logger.info(f"Checking SSL vulnerabilities for {host}:{port}")
            
            # Create SSL context
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            # Connect and get certificate info
            with socket.create_connection((host, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=host) as ssock:
                    cert = ssock.getpeercert()
                    cipher = ssock.cipher()
                    version = ssock.version()
                    
                    # Check for weak protocols
                    if version in ['SSLv2', 'SSLv3', 'TLSv1', 'TLSv1.1']:
                        vulnerabilities.append({
                            'name': 'Weak SSL/TLS Protocol',
                            'severity': 'high',
                            'description': f'Weak protocol {version} is supported',
                            'owasp_category': 'A02',
                            'cve': 'CVE-2014-3566' if version == 'SSLv3' else None,
                            'recommendation': 'Disable weak protocols and use TLS 1.2 or higher'
                        })
                    
                    # Check for weak ciphers
                    if cipher and len(cipher) >= 3:
                        cipher_suite = cipher[0]
                        if any(weak in cipher_suite for weak in ['RC4', 'DES', 'MD5', 'NULL']):
                            vulnerabilities.append({
                                'name': 'Weak Cipher Suite',
                                'severity': 'medium',
                                'description': f'Weak cipher suite detected: {cipher_suite}',
                                'owasp_category': 'A02',
                                'recommendation': 'Configure strong cipher suites only'
                            })
                    
                    # Check certificate validity
                    if cert:
                        not_after = datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                        if not_after < datetime.now():
                            vulnerabilities.append({
                                'name': 'Expired SSL Certificate',
                                'severity': 'high',
                                'description': f'SSL certificate expired on {cert["notAfter"]}',
                                'owasp_category': 'A02',
                                'recommendation': 'Renew SSL certificate immediately'
                            })
                        
                        # Check for self-signed certificate
                        if cert.get('issuer') == cert.get('subject'):
                            vulnerabilities.append({
                                'name': 'Self-Signed Certificate',
                                'severity': 'medium',
                                'description': 'Self-signed SSL certificate detected',
                                'owasp_category': 'A02',
                                'recommendation': 'Use certificate from trusted CA'
                            })
        
        except Exception as e:
            logger.warning(f"SSL check failed for {host}:{port} - {str(e)}")
        
        return vulnerabilities
    
    def check_web_vulnerabilities(self, url: str) -> List[Dict]:
        """
        Check for common web application vulnerabilities
        Based on OWASP Top 10
        """
        vulnerabilities = []
        
        try:
            logger.info(f"Checking web vulnerabilities for {url}")
            
            # Basic HTTP request to gather information
            response = requests.get(url, timeout=10, verify=False, allow_redirects=True)
            headers = response.headers
            
            # Check for missing security headers
            security_headers = {
                'X-Content-Type-Options': 'nosniff',
                'X-Frame-Options': 'DENY',
                'X-XSS-Protection': '1; mode=block',
                'Strict-Transport-Security': 'max-age=31536000',
                'Content-Security-Policy': 'default-src',
                'Referrer-Policy': 'strict-origin-when-cross-origin'
            }
            
            for header, expected in security_headers.items():
                if header not in headers:
                    vulnerabilities.append({
                        'name': f'Missing Security Header: {header}',
                        'severity': 'medium',
                        'description': f'Security header {header} is missing',
                        'owasp_category': 'A05',
                        'recommendation': f'Add {header}: {expected} header'
                    })
            
            # Check for server information disclosure
            if 'Server' in headers:
                server_header = headers['Server']
                if any(server in server_header.lower() for server in ['apache', 'nginx', 'iis']):
                    vulnerabilities.append({
                        'name': 'Server Information Disclosure',
                        'severity': 'low',
                        'description': f'Server information disclosed: {server_header}',
                        'owasp_category': 'A05',
                        'recommendation': 'Remove or obfuscate server header'
                    })
            
            # Check for directory listing
            dir_listing_indicators = [
                'Index of /',
                'Directory Listing',
                'Parent Directory'
            ]
            
            if any(indicator in response.text for indicator in dir_listing_indicators):
                vulnerabilities.append({
                    'name': 'Directory Listing Enabled',
                    'severity': 'medium',
                    'description': 'Directory listing is enabled',
                    'owasp_category': 'A05',
                    'recommendation': 'Disable directory listing'
                })
            
            # Check for common vulnerable paths
            vulnerable_paths = [
                '/admin',
                '/administrator',
                '/wp-admin',
                '/phpmyadmin',
                '/config.php',
                '/.env',
                '/backup',
                '/test'
            ]
            
            for path in vulnerable_paths:
                try:
                    test_url = url.rstrip('/') + path
                    test_response = requests.get(test_url, timeout=5, verify=False)
                    if test_response.status_code == 200:
                        vulnerabilities.append({
                            'name': f'Exposed Sensitive Path: {path}',
                            'severity': 'high',
                            'description': f'Sensitive path {path} is accessible',
                            'owasp_category': 'A01',
                            'recommendation': f'Restrict access to {path}'
                        })
                except:
                    pass
            
            # Check for HTTP methods
            try:
                options_response = requests.options(url, timeout=5, verify=False)
                if 'Allow' in options_response.headers:
                    allowed_methods = options_response.headers['Allow'].split(', ')
                    dangerous_methods = ['PUT', 'DELETE', 'TRACE', 'CONNECT']
                    
                    for method in dangerous_methods:
                        if method in allowed_methods:
                            vulnerabilities.append({
                                'name': f'Dangerous HTTP Method Enabled: {method}',
                                'severity': 'medium',
                                'description': f'HTTP method {method} is enabled',
                                'owasp_category': 'A05',
                                'recommendation': f'Disable {method} method'
                            })
            except:
                pass
        
        except Exception as e:
            logger.warning(f"Web vulnerability check failed for {url} - {str(e)}")
        
        return vulnerabilities
    
    def check_network_services(self, host: str) -> List[Dict]:
        """
        Check for vulnerable network services
        """
        vulnerabilities = []
        
        # Common vulnerable services and their default ports
        vulnerable_services = {
            21: {'name': 'FTP', 'severity': 'medium'},
            23: {'name': 'Telnet', 'severity': 'high'},
            25: {'name': 'SMTP', 'severity': 'low'},
            53: {'name': 'DNS', 'severity': 'low'},
            69: {'name': 'TFTP', 'severity': 'high'},
            79: {'name': 'Finger', 'severity': 'medium'},
            110: {'name': 'POP3', 'severity': 'low'},
            111: {'name': 'RPC', 'severity': 'medium'},
            135: {'name': 'RPC Endpoint Mapper', 'severity': 'medium'},
            139: {'name': 'NetBIOS', 'severity': 'medium'},
            143: {'name': 'IMAP', 'severity': 'low'},
            161: {'name': 'SNMP', 'severity': 'medium'},
            445: {'name': 'SMB', 'severity': 'high'},
            512: {'name': 'rexec', 'severity': 'high'},
            513: {'name': 'rlogin', 'severity': 'high'},
            514: {'name': 'rsh', 'severity': 'high'},
            1433: {'name': 'MSSQL', 'severity': 'medium'},
            1521: {'name': 'Oracle', 'severity': 'medium'},
            2049: {'name': 'NFS', 'severity': 'medium'},
            3306: {'name': 'MySQL', 'severity': 'medium'},
            3389: {'name': 'RDP', 'severity': 'medium'},
            5432: {'name': 'PostgreSQL', 'severity': 'medium'},
            5900: {'name': 'VNC', 'severity': 'high'},
            6379: {'name': 'Redis', 'severity': 'medium'}
        }
        
        logger.info(f"Checking network services for {host}")
        
        for port, service_info in vulnerable_services.items():
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(3)
                result = sock.connect_ex((host, port))
                sock.close()
                
                if result == 0:  # Port is open
                    vulnerabilities.append({
                        'name': f'Potentially Vulnerable Service: {service_info["name"]}',
                        'severity': service_info['severity'],
                        'description': f'{service_info["name"]} service running on port {port}',
                        'port': port,
                        'owasp_category': 'A05',
                        'recommendation': f'Review {service_info["name"]} configuration and access controls'
                    })
                    logger.info(f"Found open port {port} ({service_info['name']}) on {host}")
            
            except Exception as e:
                continue
        
        return vulnerabilities
    
    def run_custom_checks(self) -> List[Dict]:
        """
        Run custom vulnerability checks
        """
        vulnerabilities = []
        
        # Parse target to determine scan type
        if self.target.startswith('http'):
            # Web application target
            vulnerabilities.extend(self.check_web_vulnerabilities(self.target))
            
            # Check SSL if HTTPS
            if self.target.startswith('https'):
                parsed_url = urlparse(self.target)
                host = parsed_url.hostname
                port = parsed_url.port or 443
                vulnerabilities.extend(self.check_ssl_vulnerabilities(host, port))
        
        else:
            # Network target (IP or hostname)
            vulnerabilities.extend(self.check_network_services(self.target))
            
            # Check SSL on common HTTPS ports
            for port in [443, 8443, 9443]:
                vulnerabilities.extend(self.check_ssl_vulnerabilities(self.target, port))
        
        return vulnerabilities
    
    def generate_recommendations(self):
        """
        Generate security recommendations based on findings
        """
        recommendations = []
        
        # Count vulnerabilities by severity
        severity_counts = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0}
        owasp_counts = {}
        
        for vuln in self.results['vulnerabilities']:
            severity = vuln.get('severity', 'low')
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            owasp_cat = vuln.get('owasp_category', 'Unknown')
            owasp_counts[owasp_cat] = owasp_counts.get(owasp_cat, 0) + 1
        
        # Generate priority recommendations
        if severity_counts['critical'] > 0:
            recommendations.append({
                'priority': 'CRITICAL',
                'title': 'Address Critical Vulnerabilities Immediately',
                'description': f'Found {severity_counts["critical"]} critical vulnerabilities requiring immediate attention',
                'action': 'Patch or mitigate critical vulnerabilities within 24 hours'
            })
        
        if severity_counts['high'] > 0:
            recommendations.append({
                'priority': 'HIGH',
                'title': 'Remediate High-Risk Vulnerabilities',
                'description': f'Found {severity_counts["high"]} high-risk vulnerabilities',
                'action': 'Address high-risk vulnerabilities within 7 days'
            })
        
        # OWASP-specific recommendations
        if 'A02' in owasp_counts:
            recommendations.append({
                'priority': 'HIGH',
                'title': 'Strengthen Cryptographic Controls',
                'description': 'Multiple cryptographic weaknesses detected',
                'action': 'Review and update SSL/TLS configuration, use strong ciphers'
            })
        
        if 'A05' in owasp_counts:
            recommendations.append({
                'priority': 'MEDIUM',
                'title': 'Fix Security Misconfigurations',
                'description': 'Security configuration issues detected',
                'action': 'Review server and application configurations'
            })
        
        self.results['recommendations'] = recommendations
    
    def generate_report(self, output_format: str = 'json', filename: str = None):
        """
        Generate comprehensive vulnerability assessment report
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"vulnerability_report_{timestamp}"
        
        # Calculate summary
        total_vulns = len(self.results['vulnerabilities'])
        severity_counts = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0}
        
        for vuln in self.results['vulnerabilities']:
            severity = vuln.get('severity', 'low')
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        self.results['summary'] = {
            'total_vulnerabilities': total_vulns,
            'critical_count': severity_counts['critical'],
            'high_count': severity_counts['high'],
            'medium_count': severity_counts['medium'],
            'low_count': severity_counts['low'],
            'risk_score': self.calculate_risk_score(severity_counts)
        }
        
        if output_format.lower() == 'json':
            with open(f"{filename}.json", 'w') as f:
                json.dump(self.results, f, indent=2)
            logger.info(f"JSON report saved: {filename}.json")
    
    def calculate_risk_score(self, severity_counts: Dict) -> float:
        """
        Calculate overall risk score based on vulnerability counts
        """
        weights = {'critical': 10, 'high': 7, 'medium': 4, 'low': 1}
        total_score = sum(count * weights[severity] for severity, count in severity_counts.items())
        max_possible = 100  # Arbitrary maximum for normalization
        return min(total_score / max_possible * 100, 100)
    
    def run_scan(self):
        """
        Execute comprehensive vulnerability scan
        """
        logger.info("Starting CyberGuard Pro Vulnerability Scanner")
        
        try:
            # Run custom vulnerability checks
            vulnerabilities = self.run_custom_checks()
            self.results['vulnerabilities'] = vulnerabilities
            
            # Generate recommendations
            self.generate_recommendations()
            
            # Generate report
            self.generate_report()
            
            logger.info(f"Vulnerability scan completed. Found {len(vulnerabilities)} vulnerabilities")
            return True
            
        except Exception as e:
            logger.error(f"Vulnerability scan failed: {str(e)}")
            return False

def main():
    """Main function with command-line interface"""
    parser = argparse.ArgumentParser(
        description='CyberGuard Pro Vulnerability Scanner - Advanced VAPT Tool'
    )
    parser.add_argument('target', help='Target URL or IP address')
    parser.add_argument('--scan-type', choices=['quick', 'comprehensive', 'web'], 
                       default='comprehensive', help='Scan type')
    parser.add_argument('--output', choices=['json', 'xml', 'html'], 
                       default='json', help='Output format')
    parser.add_argument('--filename', help='Output filename (without extension)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Initialize and run scanner
    scanner = VulnerabilityScanner(args.target, args.scan_type)
    
    print(f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                CyberGuard Pro Vulnerability Scanner         ║
    ║                    Advanced VAPT Tool for ReBIT             ║
    ╠══════════════════════════════════════════════════════════════╣
    ║ Target: {args.target:<50} ║
    ║ Scan Type: {args.scan_type:<45} ║
    ║ Output: {args.output:<48} ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    success = scanner.run_scan()
    
    if success:
        summary = scanner.results['summary']
        print("\n✅ Vulnerability scan completed successfully!")
        print(f"🔍 Total vulnerabilities: {summary['total_vulnerabilities']}")
        print(f"🚨 Critical: {summary['critical_count']}")
        print(f"⚠️  High: {summary['high_count']}")
        print(f"📊 Risk Score: {summary['risk_score']:.1f}/100")
        print("📄 Report generated in current directory")
    else:
        print("\n❌ Vulnerability scan failed. Check logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
