import React from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Button,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material';
import {
  Visibility as ViewIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
} from '@mui/icons-material';

const SecurityEvents = () => {
  const [filter, setFilter] = React.useState('all');
  const [searchTerm, setSearchTerm] = React.useState('');

  // Mock security events data
  const securityEvents = [
    {
      id: 'EVT-001',
      timestamp: '2024-01-15 14:30:25',
      type: 'Authentication Failure',
      severity: 'high',
      source: '*************',
      destination: 'auth.company.com',
      description: 'Multiple failed login attempts detected',
      status: 'investigating',
      assignee: '<PERSON>',
    },
    {
      id: 'EVT-002',
      timestamp: '2024-01-15 14:25:10',
      type: 'Malware Detection',
      severity: 'critical',
      source: '*********',
      destination: 'endpoint-pc-001',
      description: 'Trojan.Win32.Agent detected and quarantined',
      status: 'resolved',
      assignee: 'Jane Smith',
    },
    {
      id: 'EVT-003',
      timestamp: '2024-01-15 14:20:45',
      type: 'Network Anomaly',
      severity: 'medium',
      source: '***********',
      destination: 'external-server.com',
      description: 'Unusual outbound traffic pattern detected',
      status: 'monitoring',
      assignee: 'Mike Johnson',
    },
    {
      id: 'EVT-004',
      timestamp: '2024-01-15 14:15:30',
      type: 'Policy Violation',
      severity: 'low',
      source: '<EMAIL>',
      destination: 'file-server',
      description: 'Unauthorized file access attempt',
      status: 'closed',
      assignee: 'Sarah Wilson',
    },
    {
      id: 'EVT-005',
      timestamp: '2024-01-15 14:10:15',
      type: 'Vulnerability Scan',
      severity: 'medium',
      source: 'scanner.internal',
      destination: '***********/24',
      description: 'High-risk vulnerabilities found on 3 hosts',
      status: 'open',
      assignee: 'Tom Brown',
    },
  ];

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return 'error';
      case 'investigating': return 'warning';
      case 'monitoring': return 'info';
      case 'resolved': return 'success';
      case 'closed': return 'default';
      default: return 'default';
    }
  };

  const filteredEvents = securityEvents.filter(event => {
    const matchesFilter = filter === 'all' || event.severity === filter;
    const matchesSearch = event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.id.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  return (
    <Box>
      {/* Page Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Security Events
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Real-time security event monitoring and analysis
        </Typography>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="error">
                Critical Events
              </Typography>
              <Typography variant="h3">
                {securityEvents.filter(e => e.severity === 'critical').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="warning.main">
                High Priority
              </Typography>
              <Typography variant="h3">
                {securityEvents.filter(e => e.severity === 'high').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="info.main">
                Under Investigation
              </Typography>
              <Typography variant="h3">
                {securityEvents.filter(e => e.status === 'investigating').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="success.main">
                Resolved Today
              </Typography>
              <Typography variant="h3">
                {securityEvents.filter(e => e.status === 'resolved').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters and Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search events..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Severity Filter</InputLabel>
                <Select
                  value={filter}
                  label="Severity Filter"
                  onChange={(e) => setFilter(e.target.value)}
                >
                  <MenuItem value="all">All Severities</MenuItem>
                  <MenuItem value="critical">Critical</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="low">Low</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={5}>
              <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                <Button startIcon={<RefreshIcon />} variant="outlined">
                  Refresh
                </Button>
                <Button startIcon={<DownloadIcon />} variant="outlined">
                  Export
                </Button>
                <Button startIcon={<FilterIcon />} variant="contained">
                  Advanced Filter
                </Button>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Events Table */}
      <Card>
        <CardContent>
          <TableContainer component={Paper} elevation={0}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Event ID</TableCell>
                  <TableCell>Timestamp</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Severity</TableCell>
                  <TableCell>Source</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Assignee</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredEvents.map((event) => (
                  <TableRow key={event.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {event.id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {event.timestamp}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {event.type}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={event.severity.toUpperCase()}
                        color={getSeverityColor(event.severity)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontFamily="monospace">
                        {event.source}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {event.description}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={event.status.toUpperCase()}
                        color={getStatusColor(event.status)}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {event.assignee}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <IconButton size="small" color="primary">
                        <ViewIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default SecurityEvents;
