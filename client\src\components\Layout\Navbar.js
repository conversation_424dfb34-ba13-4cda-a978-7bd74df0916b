import React from 'react';
import {
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Badge,
  Box,
  Avatar,
  Menu,
  MenuItem,
  Di<PERSON><PERSON>,
  Chip,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  AccountCircle,
  Settings,
  Logout,
  Warning,
  Error,
} from '@mui/icons-material';

const Navbar = ({ onSidebarToggle }) => {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [notificationAnchor, setNotificationAnchor] = React.useState(null);

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleNotificationMenuOpen = (event) => {
    setNotificationAnchor(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setNotificationAnchor(null);
  };

  // Mock security status
  const securityStatus = {
    level: 'HIGH',
    color: 'error',
    incidents: 3,
    alerts: 12,
  };

  // Mock notifications
  const notifications = [
    {
      id: 1,
      type: 'critical',
      title: 'Critical Security Alert',
      message: 'Suspicious login attempt detected',
      time: '2 minutes ago',
    },
    {
      id: 2,
      type: 'warning',
      title: 'Vulnerability Scan Complete',
      message: '5 high-risk vulnerabilities found',
      time: '15 minutes ago',
    },
    {
      id: 3,
      type: 'info',
      title: 'System Update',
      message: 'Security patches applied successfully',
      time: '1 hour ago',
    },
  ];

  return (
    <AppBar
      position="fixed"
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 1,
        backgroundColor: 'primary.main',
      }}
    >
      <Toolbar>
        <IconButton
          color="inherit"
          aria-label="toggle sidebar"
          onClick={onSidebarToggle}
          edge="start"
          sx={{ mr: 2 }}
        >
          <MenuIcon />
        </IconButton>

        <SecurityIcon sx={{ mr: 1 }} />
        <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
          CyberGuard Pro
        </Typography>

        {/* Security Status Indicator */}
        <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
          <Chip
            icon={securityStatus.level === 'HIGH' ? <Error /> : <Warning />}
            label={`Security Level: ${securityStatus.level}`}
            color={securityStatus.color}
            variant="outlined"
            size="small"
            sx={{ 
              color: 'white', 
              borderColor: 'white',
              '& .MuiChip-icon': { color: 'white' }
            }}
          />
        </Box>

        {/* Notifications */}
        <IconButton
          color="inherit"
          onClick={handleNotificationMenuOpen}
          sx={{ mr: 1 }}
        >
          <Badge badgeContent={notifications.length} color="error">
            <NotificationsIcon />
          </Badge>
        </IconButton>

        {/* Profile Menu */}
        <IconButton
          color="inherit"
          onClick={handleProfileMenuOpen}
          sx={{ p: 0 }}
        >
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>
            <AccountCircle />
          </Avatar>
        </IconButton>

        {/* Profile Menu Dropdown */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          onClick={handleMenuClose}
          PaperProps={{
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1,
              },
            },
          }}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          <MenuItem>
            <Avatar /> Security Analyst
          </MenuItem>
          <Divider />
          <MenuItem>
            <Settings fontSize="small" sx={{ mr: 1 }} />
            Settings
          </MenuItem>
          <MenuItem>
            <Logout fontSize="small" sx={{ mr: 1 }} />
            Logout
          </MenuItem>
        </Menu>

        {/* Notifications Menu */}
        <Menu
          anchorEl={notificationAnchor}
          open={Boolean(notificationAnchor)}
          onClose={handleMenuClose}
          PaperProps={{
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              minWidth: 300,
            },
          }}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          <Box sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Security Notifications
            </Typography>
            {notifications.map((notification) => (
              <Box key={notification.id} sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.primary">
                  {notification.title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {notification.message}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {notification.time}
                </Typography>
                <Divider sx={{ mt: 1 }} />
              </Box>
            ))}
          </Box>
        </Menu>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
