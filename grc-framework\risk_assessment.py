#!/usr/bin/env python3
"""
CyberGuard Pro - Risk Assessment Framework
Comprehensive risk analysis and management system
Implements NIST, ISO 27001, and COBIT frameworks
"""

import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import logging
import argparse
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('../logs/risk_assessment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RiskCategory(Enum):
    """Risk categories based on NIST framework"""
    OPERATIONAL = "Operational"
    TECHNICAL = "Technical"
    MANAGEMENT = "Management"
    EXTERNAL = "External"
    COMPLIANCE = "Compliance"

class RiskLevel(Enum):
    """Risk levels with numerical values"""
    VERY_LOW = (1, "Very Low")
    LOW = (2, "Low")
    MEDIUM = (3, "Medium")
    HIGH = (4, "High")
    VERY_HIGH = (5, "Very High")
    
    def __init__(self, value, label):
        self.value = value
        self.label = label

@dataclass
class RiskFactor:
    """Individual risk factor definition"""
    id: str
    name: str
    description: str
    category: RiskCategory
    likelihood: int  # 1-5 scale
    impact: int      # 1-5 scale
    current_controls: List[str]
    residual_risk: Optional[int] = None
    owner: str = ""
    due_date: Optional[str] = None
    status: str = "Open"
    
    def calculate_inherent_risk(self) -> int:
        """Calculate inherent risk score (likelihood × impact)"""
        return self.likelihood * self.impact
    
    def calculate_residual_risk(self, control_effectiveness: float = 0.7) -> int:
        """Calculate residual risk after controls"""
        inherent = self.calculate_inherent_risk()
        return max(1, int(inherent * (1 - control_effectiveness)))
    
    def get_risk_level(self, score: int) -> RiskLevel:
        """Convert risk score to risk level"""
        if score <= 5:
            return RiskLevel.VERY_LOW
        elif score <= 10:
            return RiskLevel.LOW
        elif score <= 15:
            return RiskLevel.MEDIUM
        elif score <= 20:
            return RiskLevel.HIGH
        else:
            return RiskLevel.VERY_HIGH

class RiskAssessmentFramework:
    """
    Comprehensive risk assessment framework
    Implements industry standards and best practices
    """
    
    def __init__(self, organization: str = "CyberGuard Pro"):
        self.organization = organization
        self.assessment_date = datetime.now()
        self.risk_factors = []
        self.risk_matrix = None
        self.compliance_frameworks = ['NIST-CSF', 'ISO-27001', 'COBIT-2019', 'RBI-Guidelines']
        
        # Initialize with common cybersecurity risks
        self._initialize_common_risks()
    
    def _initialize_common_risks(self):
        """Initialize with common cybersecurity risk factors"""
        common_risks = [
            {
                'id': 'RISK-001',
                'name': 'Data Breach',
                'description': 'Unauthorized access to sensitive data',
                'category': RiskCategory.TECHNICAL,
                'likelihood': 3,
                'impact': 5,
                'current_controls': ['Encryption', 'Access Controls', 'Monitoring'],
                'owner': 'CISO'
            },
            {
                'id': 'RISK-002',
                'name': 'Ransomware Attack',
                'description': 'Malicious encryption of organizational data',
                'category': RiskCategory.TECHNICAL,
                'likelihood': 4,
                'impact': 5,
                'current_controls': ['Backup Systems', 'Endpoint Protection', 'User Training'],
                'owner': 'IT Security Manager'
            },
            {
                'id': 'RISK-003',
                'name': 'Insider Threat',
                'description': 'Malicious or negligent actions by employees',
                'category': RiskCategory.OPERATIONAL,
                'likelihood': 2,
                'impact': 4,
                'current_controls': ['Background Checks', 'Access Reviews', 'Monitoring'],
                'owner': 'HR Manager'
            },
            {
                'id': 'RISK-004',
                'name': 'Third-Party Vendor Risk',
                'description': 'Security risks from external vendors',
                'category': RiskCategory.EXTERNAL,
                'likelihood': 3,
                'impact': 3,
                'current_controls': ['Vendor Assessments', 'Contracts', 'Monitoring'],
                'owner': 'Procurement Manager'
            },
            {
                'id': 'RISK-005',
                'name': 'Regulatory Non-Compliance',
                'description': 'Failure to meet regulatory requirements',
                'category': RiskCategory.COMPLIANCE,
                'likelihood': 2,
                'impact': 4,
                'current_controls': ['Compliance Monitoring', 'Regular Audits', 'Training'],
                'owner': 'Compliance Officer'
            },
            {
                'id': 'RISK-006',
                'name': 'System Availability',
                'description': 'Critical system downtime or unavailability',
                'category': RiskCategory.OPERATIONAL,
                'likelihood': 3,
                'impact': 4,
                'current_controls': ['Redundancy', 'Backup Systems', 'Monitoring'],
                'owner': 'IT Operations Manager'
            },
            {
                'id': 'RISK-007',
                'name': 'Phishing Attacks',
                'description': 'Social engineering attacks via email',
                'category': RiskCategory.TECHNICAL,
                'likelihood': 4,
                'impact': 3,
                'current_controls': ['Email Filtering', 'User Training', 'MFA'],
                'owner': 'Security Awareness Manager'
            },
            {
                'id': 'RISK-008',
                'name': 'Cloud Security Misconfiguration',
                'description': 'Improper cloud service configurations',
                'category': RiskCategory.TECHNICAL,
                'likelihood': 3,
                'impact': 4,
                'current_controls': ['Configuration Reviews', 'Automated Scanning', 'Training'],
                'owner': 'Cloud Security Architect'
            }
        ]
        
        for risk_data in common_risks:
            risk_factor = RiskFactor(**risk_data)
            self.risk_factors.append(risk_factor)
    
    def add_risk_factor(self, risk_factor: RiskFactor):
        """Add a new risk factor to the assessment"""
        self.risk_factors.append(risk_factor)
        logger.info(f"Added risk factor: {risk_factor.name}")
    
    def calculate_risk_scores(self):
        """Calculate risk scores for all factors"""
        for risk in self.risk_factors:
            risk.residual_risk = risk.calculate_residual_risk()
        
        logger.info("Risk scores calculated for all factors")
    
    def generate_risk_matrix(self) -> pd.DataFrame:
        """Generate risk matrix visualization data"""
        matrix_data = []
        
        for risk in self.risk_factors:
            inherent_score = risk.calculate_inherent_risk()
            residual_score = risk.calculate_residual_risk()
            
            matrix_data.append({
                'Risk ID': risk.id,
                'Risk Name': risk.name,
                'Category': risk.category.value,
                'Likelihood': risk.likelihood,
                'Impact': risk.impact,
                'Inherent Risk': inherent_score,
                'Residual Risk': residual_score,
                'Risk Level': risk.get_risk_level(residual_score).label,
                'Owner': risk.owner,
                'Controls': ', '.join(risk.current_controls)
            })
        
        self.risk_matrix = pd.DataFrame(matrix_data)
        return self.risk_matrix
    
    def create_risk_heatmap(self, save_path: str = 'risk_heatmap.png'):
        """Create risk heatmap visualization"""
        if self.risk_matrix is None:
            self.generate_risk_matrix()
        
        # Create 5x5 heatmap matrix
        heatmap_data = np.zeros((5, 5))
        
        for _, risk in self.risk_matrix.iterrows():
            likelihood = risk['Likelihood'] - 1  # Convert to 0-4 scale
            impact = risk['Impact'] - 1
            heatmap_data[4-impact][likelihood] += 1  # Flip impact axis for proper display
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(
            heatmap_data,
            annot=True,
            fmt='g',
            cmap='RdYlGn_r',
            xticklabels=['Very Low', 'Low', 'Medium', 'High', 'Very High'],
            yticklabels=['Very High', 'High', 'Medium', 'Low', 'Very Low'],
            cbar_kws={'label': 'Number of Risks'}
        )
        
        plt.title(f'{self.organization} - Risk Assessment Heatmap')
        plt.xlabel('Likelihood')
        plt.ylabel('Impact')
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Risk heatmap saved to {save_path}")
    
    def generate_compliance_report(self) -> Dict:
        """Generate compliance assessment report"""
        compliance_data = {
            'assessment_date': self.assessment_date.isoformat(),
            'organization': self.organization,
            'frameworks': {}
        }
        
        # NIST Cybersecurity Framework assessment
        nist_functions = {
            'Identify': {'score': 85, 'gaps': ['Asset inventory incomplete']},
            'Protect': {'score': 78, 'gaps': ['Access control reviews needed']},
            'Detect': {'score': 82, 'gaps': ['Enhanced monitoring required']},
            'Respond': {'score': 75, 'gaps': ['Incident response plan updates']},
            'Recover': {'score': 80, 'gaps': ['Backup testing frequency']}
        }
        
        compliance_data['frameworks']['NIST-CSF'] = {
            'overall_score': np.mean([func['score'] for func in nist_functions.values()]),
            'functions': nist_functions,
            'status': 'Partially Compliant'
        }
        
        # ISO 27001 assessment
        iso_domains = {
            'Information Security Policies': 90,
            'Organization of Information Security': 85,
            'Human Resource Security': 80,
            'Asset Management': 75,
            'Access Control': 82,
            'Cryptography': 88,
            'Physical and Environmental Security': 85,
            'Operations Security': 78,
            'Communications Security': 80,
            'System Acquisition': 75,
            'Supplier Relationships': 70,
            'Incident Management': 85,
            'Business Continuity': 80,
            'Compliance': 88
        }
        
        compliance_data['frameworks']['ISO-27001'] = {
            'overall_score': np.mean(list(iso_domains.values())),
            'domains': iso_domains,
            'status': 'Substantially Compliant'
        }
        
        return compliance_data
    
    def generate_risk_treatment_plan(self) -> List[Dict]:
        """Generate risk treatment recommendations"""
        treatment_plan = []
        
        for risk in self.risk_factors:
            residual_score = risk.calculate_residual_risk()
            risk_level = risk.get_risk_level(residual_score)
            
            if risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH]:
                treatment_plan.append({
                    'risk_id': risk.id,
                    'risk_name': risk.name,
                    'current_level': risk_level.label,
                    'priority': 'High',
                    'recommended_actions': self._get_treatment_recommendations(risk),
                    'target_completion': (datetime.now() + timedelta(days=30)).isoformat(),
                    'owner': risk.owner
                })
            elif risk_level == RiskLevel.MEDIUM:
                treatment_plan.append({
                    'risk_id': risk.id,
                    'risk_name': risk.name,
                    'current_level': risk_level.label,
                    'priority': 'Medium',
                    'recommended_actions': self._get_treatment_recommendations(risk),
                    'target_completion': (datetime.now() + timedelta(days=90)).isoformat(),
                    'owner': risk.owner
                })
        
        return treatment_plan
    
    def _get_treatment_recommendations(self, risk: RiskFactor) -> List[str]:
        """Get specific treatment recommendations for a risk"""
        recommendations = []
        
        if risk.category == RiskCategory.TECHNICAL:
            recommendations.extend([
                'Implement additional technical controls',
                'Enhance monitoring and detection capabilities',
                'Regular vulnerability assessments',
                'Security architecture review'
            ])
        elif risk.category == RiskCategory.OPERATIONAL:
            recommendations.extend([
                'Update operational procedures',
                'Enhance staff training programs',
                'Implement additional oversight controls',
                'Regular process reviews'
            ])
        elif risk.category == RiskCategory.COMPLIANCE:
            recommendations.extend([
                'Compliance gap analysis',
                'Policy updates and training',
                'Regular compliance audits',
                'Regulatory liaison activities'
            ])
        
        return recommendations[:3]  # Return top 3 recommendations
    
    def export_assessment(self, filename: str = None):
        """Export complete risk assessment"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'risk_assessment_{timestamp}.json'
        
        # Calculate scores
        self.calculate_risk_scores()
        
        # Generate all components
        risk_matrix = self.generate_risk_matrix()
        compliance_report = self.generate_compliance_report()
        treatment_plan = self.generate_risk_treatment_plan()
        
        # Create comprehensive report
        assessment_report = {
            'metadata': {
                'organization': self.organization,
                'assessment_date': self.assessment_date.isoformat(),
                'assessor': 'CyberGuard Pro Risk Assessment Framework',
                'version': '1.0'
            },
            'executive_summary': {
                'total_risks': len(self.risk_factors),
                'high_risks': len([r for r in self.risk_factors if r.get_risk_level(r.calculate_residual_risk()) in [RiskLevel.HIGH, RiskLevel.VERY_HIGH]]),
                'medium_risks': len([r for r in self.risk_factors if r.get_risk_level(r.calculate_residual_risk()) == RiskLevel.MEDIUM]),
                'low_risks': len([r for r in self.risk_factors if r.get_risk_level(r.calculate_residual_risk()) in [RiskLevel.LOW, RiskLevel.VERY_LOW]])
            },
            'risk_factors': [asdict(risk) for risk in self.risk_factors],
            'risk_matrix': risk_matrix.to_dict('records'),
            'compliance_assessment': compliance_report,
            'treatment_plan': treatment_plan
        }
        
        # Save to file
        with open(filename, 'w') as f:
            json.dump(assessment_report, f, indent=2, default=str)
        
        # Create visualizations
        self.create_risk_heatmap()
        
        logger.info(f"Risk assessment exported to {filename}")
        return assessment_report

def main():
    """Main function for command-line usage"""
    parser = argparse.ArgumentParser(
        description='CyberGuard Pro Risk Assessment Framework'
    )
    parser.add_argument('--organization', default='CyberGuard Pro', 
                       help='Organization name')
    parser.add_argument('--output', default='risk_assessment.json',
                       help='Output filename')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    print(f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                CyberGuard Pro Risk Assessment                ║
    ║                    GRC Framework for ReBIT                   ║
    ╠══════════════════════════════════════════════════════════════╣
    ║ Organization: {args.organization:<44} ║
    ║ Output: {args.output:<50} ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Initialize framework
    framework = RiskAssessmentFramework(args.organization)
    
    # Run assessment
    report = framework.export_assessment(args.output)
    
    # Display summary
    summary = report['executive_summary']
    print(f"\n✅ Risk Assessment Completed!")
    print(f"📊 Total Risks: {summary['total_risks']}")
    print(f"🚨 High Risks: {summary['high_risks']}")
    print(f"⚠️  Medium Risks: {summary['medium_risks']}")
    print(f"✅ Low Risks: {summary['low_risks']}")
    print(f"📄 Report saved: {args.output}")
    print(f"📈 Heatmap saved: risk_heatmap.png")

if __name__ == "__main__":
    main()
