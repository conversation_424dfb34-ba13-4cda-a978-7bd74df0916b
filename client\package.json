{"name": "cyberguard-client", "version": "1.0.0", "description": "CyberGuard Pro - SOC Dashboard Frontend", "private": true, "dependencies": {"@mui/material": "^5.14.18", "@mui/icons-material": "^5.14.18", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-charts": "^6.18.1", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "notistack": "^3.0.1", "react-helmet": "^6.1.0", "lodash": "^4.17.21", "crypto-js": "^4.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "web-vitals": "^2.1.4"}, "proxy": "http://localhost:3000"}