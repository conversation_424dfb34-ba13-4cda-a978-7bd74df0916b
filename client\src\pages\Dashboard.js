import React from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Chip,
  Alert,
  Paper,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  TrendingUp as TrendingUpIcon,
  NetworkCheck as NetworkIcon,
  Shield as ShieldIcon,
  BugReport as BugIcon,
} from '@mui/icons-material';

// Import dashboard components
import SecurityMetricsCard from '../components/Dashboard/SecurityMetricsCard';
import ThreatIntelligenceCard from '../components/Dashboard/ThreatIntelligenceCard';
import IncidentOverviewCard from '../components/Dashboard/IncidentOverviewCard';
import ComplianceStatusCard from '../components/Dashboard/ComplianceStatusCard';
import RecentAlertsCard from '../components/Dashboard/RecentAlertsCard';
import NetworkStatusCard from '../components/Dashboard/NetworkStatusCard';

const Dashboard = () => {
  // Mock data for demonstration
  const securityMetrics = {
    threatLevel: 'MEDIUM',
    activeIncidents: 3,
    resolvedToday: 12,
    vulnerabilities: {
      critical: 2,
      high: 8,
      medium: 15,
      low: 23,
    },
    complianceScore: 87,
  };

  const recentAlerts = [
    {
      id: 1,
      severity: 'critical',
      title: 'Suspicious Login Activity',
      description: 'Multiple failed login attempts from unknown IP',
      timestamp: '2 minutes ago',
      source: 'Authentication System',
    },
    {
      id: 2,
      severity: 'high',
      title: 'Malware Detection',
      description: 'Potential malware detected on endpoint',
      timestamp: '15 minutes ago',
      source: 'Endpoint Protection',
    },
    {
      id: 3,
      severity: 'medium',
      title: 'Unusual Network Traffic',
      description: 'Abnormal data transfer patterns detected',
      timestamp: '1 hour ago',
      source: 'Network Monitor',
    },
  ];

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical': return <ErrorIcon />;
      case 'high': return <WarningIcon />;
      case 'medium': return <SecurityIcon />;
      case 'low': return <CheckCircleIcon />;
      default: return <SecurityIcon />;
    }
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Page Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Security Operations Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Real-time cybersecurity monitoring and incident management
        </Typography>
      </Box>

      {/* Security Status Alert */}
      <Alert 
        severity={securityMetrics.threatLevel === 'HIGH' ? 'error' : 'warning'} 
        sx={{ mb: 3 }}
        icon={<SecurityIcon />}
      >
        Current Threat Level: <strong>{securityMetrics.threatLevel}</strong> - 
        {securityMetrics.activeIncidents} active incidents requiring attention
      </Alert>

      <Grid container spacing={3}>
        {/* Security Metrics Overview */}
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SecurityIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Active Incidents</Typography>
              </Box>
              <Typography variant="h3" color="error.main">
                {securityMetrics.activeIncidents}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {securityMetrics.resolvedToday} resolved today
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <BugIcon color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6">Critical Vulnerabilities</Typography>
              </Box>
              <Typography variant="h3" color="warning.main">
                {securityMetrics.vulnerabilities.critical}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {securityMetrics.vulnerabilities.high} high priority
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ShieldIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">Compliance Score</Typography>
              </Box>
              <Typography variant="h3" color="success.main">
                {securityMetrics.complianceScore}%
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={securityMetrics.complianceScore} 
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <NetworkIcon color="info" sx={{ mr: 1 }} />
                <Typography variant="h6">Network Status</Typography>
              </Box>
              <Chip 
                label="SECURE" 
                color="success" 
                icon={<CheckCircleIcon />}
                sx={{ mb: 1 }}
              />
              <Typography variant="body2" color="text.secondary">
                All systems operational
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Vulnerability Breakdown */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Vulnerability Distribution
              </Typography>
              <Grid container spacing={2}>
                {Object.entries(securityMetrics.vulnerabilities).map(([severity, count]) => (
                  <Grid item xs={6} key={severity}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      {getSeverityIcon(severity)}
                      <Typography variant="body2" sx={{ ml: 1, textTransform: 'capitalize' }}>
                        {severity}
                      </Typography>
                    </Box>
                    <Typography variant="h4" color={`${getSeverityColor(severity)}.main`}>
                      {count}
                    </Typography>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Security Alerts */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Security Alerts
              </Typography>
              {recentAlerts.map((alert) => (
                <Paper 
                  key={alert.id} 
                  elevation={1} 
                  sx={{ p: 2, mb: 2, borderLeft: `4px solid`, borderLeftColor: `${getSeverityColor(alert.severity)}.main` }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Chip 
                      label={alert.severity.toUpperCase()} 
                      color={getSeverityColor(alert.severity)}
                      size="small"
                      sx={{ mr: 1 }}
                    />
                    <Typography variant="body2" color="text.secondary">
                      {alert.timestamp}
                    </Typography>
                  </Box>
                  <Typography variant="subtitle2" gutterBottom>
                    {alert.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {alert.description}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Source: {alert.source}
                  </Typography>
                </Paper>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Additional Dashboard Components */}
        <Grid item xs={12} md={4}>
          <SecurityMetricsCard />
        </Grid>
        
        <Grid item xs={12} md={4}>
          <ThreatIntelligenceCard />
        </Grid>
        
        <Grid item xs={12} md={4}>
          <ComplianceStatusCard />
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
