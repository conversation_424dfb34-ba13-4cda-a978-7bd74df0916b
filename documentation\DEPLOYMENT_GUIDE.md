# CyberGuard Pro - Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the CyberGuard Pro cybersecurity management platform. The platform demonstrates comprehensive cybersecurity capabilities suitable for ReBIT's Engineer Trainee position requirements.

## Prerequisites

### System Requirements
- **Operating System**: Windows 10/11, macOS 10.15+, or Linux (Ubuntu 20.04+)
- **Memory**: Minimum 8GB RAM (16GB recommended)
- **Storage**: 50GB available disk space
- **Network**: Internet connection for external integrations

### Software Dependencies
- **Node.js**: Version 18.0 or higher
- **Python**: Version 3.9 or higher
- **Docker**: Version 20.10 or higher
- **Docker Compose**: Version 2.0 or higher
- **PostgreSQL**: Version 14 or higher
- **Redis**: Version 7 or higher

## Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/cyberguard-pro.git
cd cyberguard-pro
```

### 2. Environment Configuration

Copy the environment template and configure your settings:

```bash
cp .env.example .env
```

Edit the `.env` file with your specific configuration:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cyberguard
DB_USER=cyberguard_user
DB_PASSWORD=your_secure_password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Application Configuration
NODE_ENV=development
PORT=3000
JWT_SECRET=your_super_secret_jwt_key

# External API Keys (Optional)
VIRUSTOTAL_API_KEY=your_virustotal_api_key
SHODAN_API_KEY=your_shodan_api_key
```

### 3. Install Dependencies

#### Backend Dependencies
```bash
npm install
```

#### Frontend Dependencies
```bash
cd client
npm install
cd ..
```

#### Python Dependencies
```bash
cd scripts
pip install -r requirements.txt
cd ..
```

### 4. Database Setup

#### Using Docker (Recommended)
```bash
docker-compose up -d postgres redis
```

#### Manual Setup
1. Install PostgreSQL and Redis
2. Create database and user:
```sql
CREATE DATABASE cyberguard;
CREATE USER cyberguard_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE cyberguard TO cyberguard_user;
```

### 5. Initialize Database Schema

```bash
npm run db:migrate
npm run db:seed
```

### 6. Start the Application

#### Development Mode
```bash
npm run dev
```

#### Production Mode
```bash
npm run build
npm start
```

#### Using Docker
```bash
docker-compose up -d
```

## Application Access

Once deployed, access the application at:
- **Frontend**: http://localhost:3001
- **Backend API**: http://localhost:3000
- **Kibana Dashboard**: http://localhost:5601
- **OpenVAS Scanner**: http://localhost:9390

## Default Credentials

### Application Login
- **Username**: <EMAIL>
- **Password**: CyberGuard2024!

### OpenVAS Scanner
- **Username**: admin
- **Password**: admin123

## Feature Configuration

### 1. Security Operations Center (SOC)

The SOC dashboard provides real-time security monitoring:

- **Security Events**: Real-time event correlation and analysis
- **Incident Management**: Automated incident response workflows
- **Threat Intelligence**: Integration with external threat feeds
- **Compliance Monitoring**: Continuous compliance assessment

### 2. Vulnerability Assessment and Penetration Testing (VAPT)

#### Network Scanner Usage
```bash
cd vapt-tools
python network_scanner.py ***********/24 --scan-type comprehensive
```

#### Vulnerability Scanner Usage
```bash
python vulnerability_scanner.py https://example.com --scan-type web
```

### 3. GRC Framework

#### Risk Assessment
```bash
cd grc-framework
python risk_assessment.py --organization "Your Organization"
```

### 4. Network Security Tools

#### CIS Benchmark Hardening
```bash
cd network-security
python cis_hardening.py --target-os ubuntu --profile level1
```

### 5. Application Security Testing

#### OWASP ZAP Integration
```bash
cd app-security
python owasp_scanner.py --target https://example.com
```

## Security Configuration

### 1. SSL/TLS Setup

For production deployment, configure SSL certificates:

```bash
# Generate self-signed certificate for testing
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# Copy certificates to nginx directory
cp cert.pem nginx/ssl/
cp key.pem nginx/ssl/
```

### 2. Firewall Configuration

Configure firewall rules for production:

```bash
# Allow HTTP and HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Allow SSH (if needed)
sudo ufw allow 22

# Enable firewall
sudo ufw enable
```

### 3. Security Headers

The application includes security headers by default:
- Content Security Policy (CSP)
- X-Frame-Options
- X-Content-Type-Options
- Strict-Transport-Security

## Monitoring and Logging

### 1. Application Logs

Logs are stored in the `logs/` directory:
- `app.log`: Application logs
- `security.log`: Security event logs
- `error.log`: Error logs

### 2. ELK Stack Integration

The platform includes ELK stack for log analysis:
- **Elasticsearch**: Log storage and indexing
- **Logstash**: Log processing and transformation
- **Kibana**: Log visualization and analysis

### 3. Metrics Collection

Prometheus metrics are available at:
- http://localhost:3000/metrics

## Backup and Recovery

### 1. Database Backup

```bash
# Create backup
pg_dump -h localhost -U cyberguard_user cyberguard > backup.sql

# Restore backup
psql -h localhost -U cyberguard_user cyberguard < backup.sql
```

### 2. Application Data Backup

```bash
# Backup uploads and logs
tar -czf backup_$(date +%Y%m%d).tar.gz uploads/ logs/
```

## Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Find process using port
netstat -tulpn | grep :3000

# Kill process
kill -9 <process_id>
```

#### 2. Database Connection Issues
- Verify PostgreSQL is running
- Check database credentials in `.env`
- Ensure database exists and user has permissions

#### 3. Permission Issues
```bash
# Fix file permissions
chmod +x scripts/*.py
chmod +x vapt-tools/*.py
```

#### 4. Python Module Issues
```bash
# Reinstall Python dependencies
pip install --upgrade -r scripts/requirements.txt
```

### Log Analysis

Check application logs for errors:
```bash
# View recent logs
tail -f logs/app.log

# Search for errors
grep -i error logs/app.log
```

## Performance Optimization

### 1. Database Optimization

```sql
-- Create indexes for better performance
CREATE INDEX idx_security_events_timestamp ON security_events(timestamp);
CREATE INDEX idx_vulnerabilities_severity ON vulnerabilities(severity);
```

### 2. Redis Configuration

Optimize Redis for caching:
```redis
# Set memory policy
CONFIG SET maxmemory-policy allkeys-lru

# Set max memory
CONFIG SET maxmemory 1gb
```

### 3. Application Tuning

- Enable gzip compression
- Configure connection pooling
- Implement caching strategies
- Optimize database queries

## Security Hardening

### 1. Application Security

- Change default passwords
- Enable rate limiting
- Configure CORS properly
- Implement input validation
- Use HTTPS in production

### 2. System Security

- Keep system updated
- Configure fail2ban
- Disable unnecessary services
- Implement network segmentation
- Regular security audits

## Integration Guide

### 1. External Security Tools

#### SIEM Integration
```javascript
// Example SIEM integration
const siem = require('./integrations/siem');
siem.sendEvent({
  timestamp: new Date(),
  severity: 'high',
  event_type: 'security_alert',
  description: 'Suspicious activity detected'
});
```

#### Threat Intelligence Feeds
```python
# Example threat intelligence integration
from integrations.threat_intel import ThreatIntelligence

ti = ThreatIntelligence()
indicators = ti.get_latest_indicators()
```

### 2. API Integration

The platform provides REST APIs for integration:

```bash
# Get security events
curl -H "Authorization: Bearer <token>" \
     http://localhost:3000/api/security/events

# Create incident
curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer <token>" \
     -d '{"title":"Security Incident","severity":"high"}' \
     http://localhost:3000/api/incidents
```

## Maintenance

### 1. Regular Updates

```bash
# Update application dependencies
npm update
pip install --upgrade -r scripts/requirements.txt

# Update Docker images
docker-compose pull
docker-compose up -d
```

### 2. Database Maintenance

```sql
-- Vacuum and analyze database
VACUUM ANALYZE;

-- Update statistics
ANALYZE;
```

### 3. Log Rotation

Configure log rotation to prevent disk space issues:
```bash
# Add to /etc/logrotate.d/cyberguard
/path/to/cyberguard/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 cyberguard cyberguard
}
```

## Support and Documentation

### Additional Resources

- **API Documentation**: http://localhost:3000/api-docs
- **User Manual**: `documentation/USER_MANUAL.md`
- **Security Policies**: `documentation/SECURITY_POLICIES.md`
- **Architecture Guide**: `documentation/ARCHITECTURE.md`

### Getting Help

For technical support:
1. Check the troubleshooting section
2. Review application logs
3. Consult the documentation
4. Contact the development team

---

**Note**: This deployment guide is part of the CyberGuard Pro project demonstrating cybersecurity expertise for the ReBIT Engineer Trainee position. The platform showcases comprehensive security operations, GRC frameworks, and VAPT capabilities aligned with industry standards and ReBIT requirements.
