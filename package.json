{"name": "cyberguard-pro", "version": "1.0.0", "description": "Comprehensive Cybersecurity Management Platform for ReBIT Career Opportunity", "main": "server.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "nodemon server/server.js", "client": "cd client && npm start", "build": "cd client && npm run build", "test": "jest", "test:security": "npm run test:sast && npm run test:dast", "test:sast": "semgrep --config=auto .", "test:dast": "zap-baseline.py -t http://localhost:3000", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "setup": "npm install && cd client && npm install && cd ../scripts && pip install -r requirements.txt"}, "keywords": ["cybersecurity", "soc", "grc", "vapt", "security-operations", "compliance", "risk-management", "rebit"], "author": "Cybersecurity Engineer <PERSON><PERSON> Can<PERSON>date", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "redis": "^4.6.10", "socket.io": "^4.7.4", "winston": "^3.11.0", "joi": "^17.11.0", "rate-limiter-flexible": "^3.0.8", "node-cron": "^3.0.3", "multer": "^1.4.5-lts.1", "csv-parser": "^3.0.0", "pdf-lib": "^1.17.1", "nodemailer": "^6.9.7"}, "devDependencies": {"nodemon": "^3.0.2", "concurrently": "^8.2.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-security": "^1.7.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/cyberguard-pro.git"}, "bugs": {"url": "https://github.com/your-username/cyberguard-pro/issues"}, "homepage": "https://github.com/your-username/cyberguard-pro#readme"}