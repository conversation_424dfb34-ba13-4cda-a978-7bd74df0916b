import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
} from '@mui/material';
import {
  Security as SecurityIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Shield as ShieldIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';

const SecurityMetricsCard = () => {
  const metrics = {
    overallScore: 78,
    trends: [
      {
        name: 'Threat Detection',
        value: 95,
        trend: 'up',
        change: '+5%',
      },
      {
        name: 'Incident Response',
        value: 87,
        trend: 'up',
        change: '+12%',
      },
      {
        name: 'Vulnerability Management',
        value: 72,
        trend: 'down',
        change: '-3%',
      },
      {
        name: 'Compliance Adherence',
        value: 89,
        trend: 'up',
        change: '+7%',
      },
    ],
  };

  const getScoreColor = (score) => {
    if (score >= 90) return 'success';
    if (score >= 70) return 'warning';
    return 'error';
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <SecurityIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h6">Security Metrics</Typography>
        </Box>

        {/* Overall Security Score */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Box sx={{ position: 'relative', display: 'inline-flex', mr: 2 }}>
            <CircularProgress
              variant="determinate"
              value={metrics.overallScore}
              size={80}
              thickness={4}
              color={getScoreColor(metrics.overallScore)}
            />
            <Box
              sx={{
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                position: 'absolute',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Typography variant="h6" component="div" color="text.secondary">
                {metrics.overallScore}%
              </Typography>
            </Box>
          </Box>
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Overall Security Score
            </Typography>
            <Chip
              label="Good"
              color={getScoreColor(metrics.overallScore)}
              size="small"
              icon={<ShieldIcon />}
            />
          </Box>
        </Box>

        {/* Detailed Metrics */}
        <Typography variant="subtitle2" gutterBottom>
          Performance Metrics
        </Typography>
        <List dense>
          {metrics.trends.map((metric, index) => (
            <ListItem key={index} sx={{ px: 0 }}>
              <ListItemIcon sx={{ minWidth: 36 }}>
                {metric.trend === 'up' ? (
                  <TrendingUpIcon color="success" fontSize="small" />
                ) : (
                  <TrendingDownIcon color="error" fontSize="small" />
                )}
              </ListItemIcon>
              <ListItemText
                primary={metric.name}
                secondary={`${metric.value}% (${metric.change})`}
                primaryTypographyProps={{ variant: 'body2' }}
                secondaryTypographyProps={{ variant: 'caption' }}
              />
            </ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

export default SecurityMetricsCard;
