import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Avatar,
  Divider,
} from '@mui/material';
import {
  Public as PublicIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  LocationOn as LocationIcon,
} from '@mui/icons-material';

const ThreatIntelligenceCard = () => {
  const threatData = [
    {
      id: 1,
      type: 'Malware',
      severity: 'critical',
      source: 'VirusTotal',
      description: 'New ransomware variant detected',
      location: 'Global',
      timestamp: '5 minutes ago',
    },
    {
      id: 2,
      type: 'Phishing',
      severity: 'high',
      source: 'PhishTank',
      description: 'Banking phishing campaign active',
      location: 'North America',
      timestamp: '1 hour ago',
    },
    {
      id: 3,
      type: 'Botnet',
      severity: 'medium',
      source: 'Shodan',
      description: 'IoT botnet activity increased',
      location: 'Asia Pacific',
      timestamp: '3 hours ago',
    },
    {
      id: 4,
      type: 'Vulnerability',
      severity: 'high',
      source: 'CVE Database',
      description: 'Critical Apache vulnerability disclosed',
      location: 'Global',
      timestamp: '6 hours ago',
    },
  ];

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical': return <ErrorIcon />;
      case 'high': return <WarningIcon />;
      case 'medium': return <InfoIcon />;
      case 'low': return <InfoIcon />;
      default: return <InfoIcon />;
    }
  };

  const getTypeAvatar = (type) => {
    const colors = {
      'Malware': '#f44336',
      'Phishing': '#ff9800',
      'Botnet': '#9c27b0',
      'Vulnerability': '#2196f3',
    };
    
    return (
      <Avatar sx={{ bgcolor: colors[type] || '#757575', width: 32, height: 32 }}>
        {type.charAt(0)}
      </Avatar>
    );
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <PublicIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h6">Threat Intelligence</Typography>
        </Box>

        <Typography variant="body2" color="text.secondary" gutterBottom>
          Latest global threat intelligence feeds
        </Typography>

        <List dense>
          {threatData.map((threat, index) => (
            <React.Fragment key={threat.id}>
              <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                <ListItemIcon sx={{ mt: 1 }}>
                  {getTypeAvatar(threat.type)}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                      <Typography variant="subtitle2" sx={{ mr: 1 }}>
                        {threat.type}
                      </Typography>
                      <Chip
                        label={threat.severity.toUpperCase()}
                        color={getSeverityColor(threat.severity)}
                        size="small"
                        icon={getSeverityIcon(threat.severity)}
                      />
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.primary" gutterBottom>
                        {threat.description}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="caption" color="text.secondary">
                          Source: {threat.source}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          •
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <LocationIcon fontSize="small" sx={{ mr: 0.5 }} />
                          <Typography variant="caption" color="text.secondary">
                            {threat.location}
                          </Typography>
                        </Box>
                        <Typography variant="caption" color="text.secondary">
                          •
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {threat.timestamp}
                        </Typography>
                      </Box>
                    </Box>
                  }
                />
              </ListItem>
              {index < threatData.length - 1 && <Divider variant="inset" component="li" />}
            </React.Fragment>
          ))}
        </List>

        <Box sx={{ mt: 2, p: 1, bgcolor: 'action.hover', borderRadius: 1 }}>
          <Typography variant="caption" color="text.secondary">
            Intelligence feeds updated every 15 minutes from multiple sources including VirusTotal, 
            PhishTank, Shodan, and CVE databases.
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ThreatIntelligenceCard;
