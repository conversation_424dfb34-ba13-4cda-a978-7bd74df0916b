#!/usr/bin/env python3
"""
CyberGuard Pro - Network Scanner
Advanced network discovery and vulnerability scanning tool
Demonstrates VAPT capabilities for ReBIT position
"""

import nmap
import json
import csv
import argparse
import logging
import sys
import socket
import threading
from datetime import datetime
from typing import Dict, List, Optional
import ipaddress
from concurrent.futures import ThreadPoolExecutor, as_completed

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('../logs/network_scanner.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class NetworkScanner:
    """
    Advanced network scanner for vulnerability assessment
    Implements industry-standard scanning techniques
    """
    
    def __init__(self, target_range: str, scan_type: str = 'comprehensive'):
        self.target_range = target_range
        self.scan_type = scan_type
        self.nm = nmap.PortScanner()
        self.results = {
            'scan_info': {
                'target': target_range,
                'scan_type': scan_type,
                'timestamp': datetime.now().isoformat(),
                'scanner_version': nmap.__version__
            },
            'hosts': [],
            'vulnerabilities': [],
            'summary': {}
        }
        
    def validate_target(self) -> bool:
        """Validate target IP range or hostname"""
        try:
            # Check if it's a valid IP range
            ipaddress.ip_network(self.target_range, strict=False)
            return True
        except ValueError:
            try:
                # Check if it's a valid hostname
                socket.gethostbyname(self.target_range)
                return True
            except socket.gaierror:
                logger.error(f"Invalid target: {self.target_range}")
                return False
    
    def host_discovery(self) -> List[str]:
        """
        Perform host discovery using multiple techniques
        - ICMP ping sweep
        - TCP SYN ping
        - UDP ping
        """
        logger.info(f"Starting host discovery for {self.target_range}")
        
        try:
            # Ping sweep to discover live hosts
            self.nm.scan(hosts=self.target_range, arguments='-sn -PE -PP -PM -PO')
            
            live_hosts = []
            for host in self.nm.all_hosts():
                if self.nm[host].state() == 'up':
                    live_hosts.append(host)
                    logger.info(f"Live host discovered: {host}")
            
            logger.info(f"Host discovery completed. Found {len(live_hosts)} live hosts")
            return live_hosts
            
        except Exception as e:
            logger.error(f"Host discovery failed: {str(e)}")
            return []
    
    def port_scan(self, host: str, port_range: str = '1-65535') -> Dict:
        """
        Comprehensive port scanning
        - TCP SYN scan
        - UDP scan
        - Service detection
        - OS fingerprinting
        """
        logger.info(f"Starting port scan for {host}")
        
        host_info = {
            'ip': host,
            'hostname': '',
            'status': 'up',
            'tcp_ports': [],
            'udp_ports': [],
            'os_info': {},
            'services': []
        }
        
        try:
            # Get hostname
            try:
                host_info['hostname'] = socket.gethostbyaddr(host)[0]
            except socket.herror:
                host_info['hostname'] = 'Unknown'
            
            # TCP SYN scan with service detection
            logger.info(f"Performing TCP scan on {host}")
            self.nm.scan(host, port_range, arguments='-sS -sV -O --version-intensity 5')
            
            if host in self.nm.all_hosts():
                host_data = self.nm[host]
                
                # Extract TCP port information
                if 'tcp' in host_data:
                    for port in host_data['tcp']:
                        port_info = {
                            'port': port,
                            'state': host_data['tcp'][port]['state'],
                            'service': host_data['tcp'][port]['name'],
                            'version': host_data['tcp'][port]['version'],
                            'product': host_data['tcp'][port]['product'],
                            'extrainfo': host_data['tcp'][port]['extrainfo']
                        }
                        host_info['tcp_ports'].append(port_info)
                        
                        # Check for potential vulnerabilities
                        self.check_service_vulnerabilities(host, port, port_info)
                
                # Extract OS information
                if 'osmatch' in host_data:
                    for osmatch in host_data['osmatch']:
                        host_info['os_info'] = {
                            'name': osmatch['name'],
                            'accuracy': osmatch['accuracy'],
                            'line': osmatch['line']
                        }
                        break
            
            # UDP scan (limited ports for performance)
            logger.info(f"Performing UDP scan on {host}")
            udp_ports = '53,67,68,69,123,135,137,138,139,161,162,445,500,514,520,631,1434,1900,4500,5353'
            self.nm.scan(host, udp_ports, arguments='-sU --top-ports 20')
            
            if host in self.nm.all_hosts() and 'udp' in self.nm[host]:
                for port in self.nm[host]['udp']:
                    port_info = {
                        'port': port,
                        'state': self.nm[host]['udp'][port]['state'],
                        'service': self.nm[host]['udp'][port]['name'],
                        'version': self.nm[host]['udp'][port]['version']
                    }
                    host_info['udp_ports'].append(port_info)
            
            logger.info(f"Port scan completed for {host}")
            return host_info
            
        except Exception as e:
            logger.error(f"Port scan failed for {host}: {str(e)}")
            host_info['status'] = 'error'
            host_info['error'] = str(e)
            return host_info
    
    def check_service_vulnerabilities(self, host: str, port: int, port_info: Dict):
        """
        Check for common service vulnerabilities
        Based on service type and version information
        """
        service = port_info['service'].lower()
        version = port_info['version']
        
        # Common vulnerable services and ports
        vulnerable_services = {
            'ssh': {
                'ports': [22],
                'checks': ['weak_auth', 'version_check']
            },
            'telnet': {
                'ports': [23],
                'checks': ['unencrypted', 'default_creds']
            },
            'ftp': {
                'ports': [21],
                'checks': ['anonymous_login', 'version_check']
            },
            'http': {
                'ports': [80, 8080, 8000],
                'checks': ['directory_traversal', 'default_pages']
            },
            'https': {
                'ports': [443, 8443],
                'checks': ['ssl_vulnerabilities', 'certificate_check']
            },
            'smb': {
                'ports': [139, 445],
                'checks': ['null_session', 'eternal_blue']
            },
            'snmp': {
                'ports': [161],
                'checks': ['default_community', 'version_check']
            }
        }
        
        if service in vulnerable_services:
            vuln_info = {
                'host': host,
                'port': port,
                'service': service,
                'version': version,
                'vulnerabilities': [],
                'severity': 'info',
                'timestamp': datetime.now().isoformat()
            }
            
            # Check for specific vulnerabilities
            if service == 'ssh' and port == 22:
                if 'OpenSSH' in version and any(v in version for v in ['7.4', '7.3', '7.2']):
                    vuln_info['vulnerabilities'].append({
                        'name': 'SSH Version Vulnerability',
                        'description': 'Potentially vulnerable SSH version detected',
                        'severity': 'medium',
                        'cve': 'CVE-2016-10708'
                    })
                    vuln_info['severity'] = 'medium'
            
            elif service == 'http' and port in [80, 8080]:
                vuln_info['vulnerabilities'].append({
                    'name': 'Unencrypted HTTP Service',
                    'description': 'HTTP service detected - data transmitted in clear text',
                    'severity': 'low',
                    'recommendation': 'Implement HTTPS encryption'
                })
            
            elif service == 'telnet':
                vuln_info['vulnerabilities'].append({
                    'name': 'Insecure Telnet Service',
                    'description': 'Telnet service detected - unencrypted remote access',
                    'severity': 'high',
                    'recommendation': 'Replace with SSH'
                })
                vuln_info['severity'] = 'high'
            
            elif service == 'ftp' and port == 21:
                vuln_info['vulnerabilities'].append({
                    'name': 'Insecure FTP Service',
                    'description': 'FTP service detected - potential for anonymous access',
                    'severity': 'medium',
                    'recommendation': 'Use SFTP or FTPS'
                })
                vuln_info['severity'] = 'medium'
            
            if vuln_info['vulnerabilities']:
                self.results['vulnerabilities'].append(vuln_info)
                logger.warning(f"Vulnerability detected on {host}:{port} - {service}")
    
    def generate_report(self, output_format: str = 'json', filename: str = None):
        """
        Generate comprehensive scan report
        Supports JSON, CSV, and HTML formats
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"network_scan_report_{timestamp}"
        
        # Calculate summary statistics
        total_hosts = len(self.results['hosts'])
        total_open_ports = sum(len(host.get('tcp_ports', [])) + len(host.get('udp_ports', [])) 
                              for host in self.results['hosts'])
        total_vulnerabilities = len(self.results['vulnerabilities'])
        
        self.results['summary'] = {
            'total_hosts_scanned': total_hosts,
            'total_open_ports': total_open_ports,
            'total_vulnerabilities': total_vulnerabilities,
            'high_severity_vulns': len([v for v in self.results['vulnerabilities'] 
                                       if v['severity'] == 'high']),
            'medium_severity_vulns': len([v for v in self.results['vulnerabilities'] 
                                         if v['severity'] == 'medium']),
            'low_severity_vulns': len([v for v in self.results['vulnerabilities'] 
                                      if v['severity'] == 'low'])
        }
        
        if output_format.lower() == 'json':
            with open(f"{filename}.json", 'w') as f:
                json.dump(self.results, f, indent=2)
            logger.info(f"JSON report saved: {filename}.json")
        
        elif output_format.lower() == 'csv':
            # Generate CSV report for vulnerabilities
            with open(f"{filename}_vulnerabilities.csv", 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['Host', 'Port', 'Service', 'Vulnerability', 'Severity', 'Description'])
                
                for vuln in self.results['vulnerabilities']:
                    for v in vuln['vulnerabilities']:
                        writer.writerow([
                            vuln['host'],
                            vuln['port'],
                            vuln['service'],
                            v['name'],
                            v['severity'],
                            v['description']
                        ])
            
            logger.info(f"CSV report saved: {filename}_vulnerabilities.csv")
    
    def run_scan(self):
        """Execute complete network scan workflow"""
        logger.info("Starting CyberGuard Pro Network Scanner")
        
        if not self.validate_target():
            logger.error("Invalid target specified")
            return False
        
        try:
            # Step 1: Host Discovery
            live_hosts = self.host_discovery()
            if not live_hosts:
                logger.warning("No live hosts found")
                return False
            
            # Step 2: Port Scanning with threading for performance
            logger.info("Starting port scanning phase")
            with ThreadPoolExecutor(max_workers=10) as executor:
                future_to_host = {
                    executor.submit(self.port_scan, host): host 
                    for host in live_hosts
                }
                
                for future in as_completed(future_to_host):
                    host = future_to_host[future]
                    try:
                        host_result = future.result()
                        self.results['hosts'].append(host_result)
                    except Exception as e:
                        logger.error(f"Port scan failed for {host}: {str(e)}")
            
            # Step 3: Generate Report
            self.generate_report('json')
            self.generate_report('csv')
            
            logger.info("Network scan completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Scan failed: {str(e)}")
            return False

def main():
    """Main function with command-line interface"""
    parser = argparse.ArgumentParser(
        description='CyberGuard Pro Network Scanner - Advanced VAPT Tool'
    )
    parser.add_argument('target', help='Target IP range or hostname (e.g., ***********/24)')
    parser.add_argument('--scan-type', choices=['quick', 'comprehensive', 'stealth'], 
                       default='comprehensive', help='Scan type')
    parser.add_argument('--output', choices=['json', 'csv', 'both'], 
                       default='both', help='Output format')
    parser.add_argument('--filename', help='Output filename (without extension)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Initialize and run scanner
    scanner = NetworkScanner(args.target, args.scan_type)
    
    print(f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    CyberGuard Pro Network Scanner            ║
    ║                  Advanced VAPT Tool for ReBIT               ║
    ╠══════════════════════════════════════════════════════════════╣
    ║ Target: {args.target:<50} ║
    ║ Scan Type: {args.scan_type:<45} ║
    ║ Output: {args.output:<48} ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    success = scanner.run_scan()
    
    if success:
        print("\n✅ Scan completed successfully!")
        print(f"📊 Results: {len(scanner.results['hosts'])} hosts scanned")
        print(f"🔍 Vulnerabilities: {len(scanner.results['vulnerabilities'])} found")
        print("📄 Reports generated in current directory")
    else:
        print("\n❌ Scan failed. Check logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
