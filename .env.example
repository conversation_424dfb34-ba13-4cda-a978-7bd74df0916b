# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cyberguard
DB_USER=cyberguard_user
DB_PASSWORD=secure_password_123

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Application Configuration
NODE_ENV=development
PORT=3000
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=24h

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>

# External Security Tools
OPENVAS_HOST=localhost
OPENVAS_PORT=9390
OPENVAS_USERNAME=admin
OPENVAS_PASSWORD=admin123

ZAP_HOST=localhost
ZAP_PORT=8080
ZAP_API_KEY=your_zap_api_key

# Elasticsearch Configuration
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=

# Kibana Configuration
KIBANA_HOST=localhost
KIBANA_PORT=5601

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,csv,json

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Security Headers
CORS_ORIGIN=http://localhost:3001
HELMET_CSP_DIRECTIVES=default-src 'self'

# API Keys for External Services
VIRUSTOTAL_API_KEY=your_virustotal_api_key
SHODAN_API_KEY=your_shodan_api_key
ABUSE_IPDB_API_KEY=your_abuseipdb_api_key

# Notification Configuration
SLACK_WEBHOOK_URL=your_slack_webhook_url
TEAMS_WEBHOOK_URL=your_teams_webhook_url

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=./backups

# Monitoring Configuration
HEALTH_CHECK_INTERVAL=30000
METRICS_COLLECTION_INTERVAL=60000

# Development Configuration
DEBUG_MODE=false
MOCK_EXTERNAL_APIS=false
