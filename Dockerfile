# CyberGuard Pro - Multi-stage Docker Build
# Optimized for production deployment

# Stage 1: Build React frontend
FROM node:18-alpine AS frontend-builder

WORKDIR /app/client

# Copy package files
COPY client/package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY client/ ./

# Build frontend
RUN npm run build

# Stage 2: Build Node.js backend
FROM node:18-alpine AS backend-builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY server/ ./server/
COPY scripts/ ./scripts/

# Stage 3: Python environment for security tools
FROM python:3.11-alpine AS python-builder

WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    gcc \
    musl-dev \
    libffi-dev \
    openssl-dev \
    nmap \
    nmap-scripts

# Copy Python requirements
COPY scripts/requirements.txt ./

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Stage 4: Final production image
FROM node:18-alpine

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    py3-pip \
    nmap \
    openssl \
    curl \
    bash

# Create app user
RUN addgroup -g 1001 -S cyberguard && \
    adduser -S cyberguard -u 1001

# Set working directory
WORKDIR /app

# Copy backend from builder
COPY --from=backend-builder /app/node_modules ./node_modules
COPY --from=backend-builder /app/server ./server
COPY --from=backend-builder /app/package*.json ./

# Copy frontend build from builder
COPY --from=frontend-builder /app/client/build ./client/build

# Copy Python environment
COPY --from=python-builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=python-builder /usr/local/bin /usr/local/bin

# Copy security tools and scripts
COPY vapt-tools/ ./vapt-tools/
COPY grc-framework/ ./grc-framework/
COPY network-security/ ./network-security/
COPY app-security/ ./app-security/
COPY scripts/ ./scripts/

# Create necessary directories
RUN mkdir -p logs uploads backups && \
    chown -R cyberguard:cyberguard /app

# Switch to non-root user
USER cyberguard

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start application
CMD ["node", "server/server.js"]
